import os
import time
import random
import json
import httpx
from functools import wraps
from typing import Any, Dict, List

from tau_bench.model_utils.api.datapoint import Datapoint
from tau_bench.model_utils.model.chat import ChatModel, Message
from tau_bench.model_utils.model.completion import approx_cost_for_datapoint, approx_prompt_str
from tau_bench.model_utils.model.general_model import wrap_temperature
from tau_bench.model_utils.model.utils import approx_num_tokens

DEFAULT_MINIMAX_MODEL = "MiniMax-Text-01"
API_KEY_ENV_VAR = "OPENAI_API_KEY"  # Using OPENAI_API_KEY for compatibility
DEFAULT_BASE_URL = "https://api.minimax.chat/v1/text/chatcompletion_v2"

# Pricing information (placeholder values - update with actual MiniMax pricing)
PRICE_PER_INPUT_TOKEN_MAP = {
    "MiniMax-Text-01": 0.0001,
}
INPUT_PRICE_PER_TOKEN_FALLBACK = 0.0001

# Latency information (placeholder values)
LATENCY_MS_PER_OUTPUT_TOKEN_MAP = {
    "MiniMax-Text-01": 50.0,
}
LATENCY_MS_PER_OUTPUT_TOKEN_FALLBACK = 50.0

# Capability scores (placeholder values)
CAPABILITY_SCORE_MAP = {
    "MiniMax-Text-01": 0.8,
}
CAPABILITY_SCORE_FALLBACK = 0.8

# Context length limits (placeholder values)
MAX_CONTEXT_LENGTH_MAP = {
    "MiniMax-Text-01": 8192,
}
MAX_CONTEXT_LENGTH_FALLBACK = 8192


def retry_on_api_error(max_retries: int = 3, base_delay: float = 1.0):
    """Decorator to retry API calls on failure with exponential backoff."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries:
                        raise e
                    
                    # Calculate delay with exponential backoff and jitter
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                    print(f"API call failed (attempt {attempt + 1}/{max_retries + 1}): {str(e)}")
                    print(f"Retrying in {delay:.2f} seconds...")
                    time.sleep(delay)
            
            return func(*args, **kwargs)
        return wrapper
    return decorator


class MinimaxModel(ChatModel):
    def __init__(
        self,
        model: str | None = None,
        api_key: str | None = None,
        base_url: str | None = None,
        temperature: float = 0.0,
        top_p: float | None = None,
        top_k: int | None = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ) -> None:
        if model is None:
            self.model = DEFAULT_MINIMAX_MODEL
        else:
            self.model = model

        if api_key is None:
            api_key = os.getenv(API_KEY_ENV_VAR)
            if api_key is None:
                raise ValueError(f"{API_KEY_ENV_VAR} environment variable is not set")

        self.api_key = api_key
        self.base_url = base_url if base_url is not None else DEFAULT_BASE_URL
        self.temperature = temperature
        self.top_p = top_p
        self.top_k = top_k
        self.max_retries = max_retries
        self.retry_delay = retry_delay

    def _convert_messages_to_minimax_format(self, messages: List[Message]) -> List[Dict[str, Any]]:
        """Convert LangChain messages to MiniMax API format."""
        minimax_messages = []
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            
            # MiniMax uses similar role names to OpenAI
            minimax_messages.append({
                "role": role,
                "content": content
            })
        
        return minimax_messages

    def generate_message(
        self,
        messages: list[Message],
        force_json: bool,
        temperature: float | None = None,
        top_p: float | None = None,
        top_k: int | None = None,
    ) -> Message:
        if temperature is None:
            temperature = self.temperature
        if top_p is None:
            top_p = self.top_p
        if top_k is None:
            top_k = self.top_k

        msgs = self.build_generate_message_state(messages)
        minimax_messages = self._convert_messages_to_minimax_format(msgs)

        # Build API call parameters for MiniMax
        api_params = {
            "model": self.model,
            "messages": minimax_messages,
            "temperature": wrap_temperature(temperature),
        }

        # Add optional parameters if they are set
        if top_p is not None:
            api_params["top_p"] = top_p
        if top_k is not None:
            api_params["top_k"] = top_k

        # MiniMax doesn't have a direct JSON mode like OpenAI
        # We'll handle JSON formatting in post-processing if needed
        if force_json:
            # Add instruction to return JSON format
            if minimax_messages and minimax_messages[-1]["role"] == "user":
                minimax_messages[-1]["content"] += "\n\nPlease respond in valid JSON format."

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        # Apply retry logic to API call
        @retry_on_api_error(max_retries=self.max_retries, base_delay=self.retry_delay)
        def make_api_call():
            with httpx.Client() as client:
                response = client.post(
                    self.base_url,
                    headers=headers,
                    json=api_params,
                    timeout=60.0
                )
                response.raise_for_status()
                return response.json()

        res_data = make_api_call()
        
        # Extract content from MiniMax response
        # MiniMax response format is similar to OpenAI
        if "choices" in res_data and len(res_data["choices"]) > 0:
            content = res_data["choices"][0].get("message", {}).get("content", "")
        else:
            content = ""

        return self.handle_generate_message_response(
            prompt=msgs, content=content, force_json=force_json
        )

    def get_approx_cost(self, dp: Datapoint) -> float:
        cost_per_token = PRICE_PER_INPUT_TOKEN_MAP.get(self.model, INPUT_PRICE_PER_TOKEN_FALLBACK)
        return approx_cost_for_datapoint(dp=dp, price_per_input_token=cost_per_token)

    def get_latency(self, dp: Datapoint) -> float:
        latency_per_output_token = LATENCY_MS_PER_OUTPUT_TOKEN_MAP.get(
            self.model, LATENCY_MS_PER_OUTPUT_TOKEN_FALLBACK
        )
        return approx_cost_for_datapoint(dp=dp, price_per_input_token=latency_per_output_token)

    def get_capability(self) -> float:
        return CAPABILITY_SCORE_MAP.get(self.model, CAPABILITY_SCORE_FALLBACK)

    def supports_dp(self, dp: Datapoint) -> bool:
        prompt = approx_prompt_str(dp)
        return approx_num_tokens(prompt) <= MAX_CONTEXT_LENGTH_MAP.get(
            self.model, MAX_CONTEXT_LENGTH_FALLBACK
        )
