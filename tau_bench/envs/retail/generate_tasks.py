#!/usr/bin/env python3
"""
<PERSON>ript to generate 2000 new training tasks for the retail environment.
"""

import json
import random
import re
from typing import Dict, List, Any, Tuple
from tau_bench.types import Task, Action

# Load data
def load_data():
    with open('tau_bench/envs/retail/data/users.json', 'r') as f:
        users = json.load(f)
    with open('tau_bench/envs/retail/data/orders.json', 'r') as f:
        orders = json.load(f)
    with open('tau_bench/envs/retail/data/products.json', 'r') as f:
        products = json.load(f)
    return users, orders, products

# Personality traits
PERSONALITY_TRAITS = [
    "logical", "independent", "relaxing", "polite", "confident", "organized", 
    "patient", "cautious", "flexible", "shy", "curious", "pessimistic",
    "optimistic", "direct", "happy", "sad", "busy", "impatient", "insecure",
    "creative", "outgoing", "messy", "rigid", "dependent"
]

# Cancellation reasons
CANCEL_REASONS = ["no longer needed", "ordered by mistake"]

# Generate random personality
def generate_personality():
    num_traits = random.randint(1, 5)
    return random.sample(PERSONALITY_TRAITS, num_traits)

# Generate user instruction prefix
def generate_user_prefix(user_id: str, users: Dict) -> str:
    user = users[user_id]
    name = f"{user['name']['first_name']} {user['name']['last_name']}"
    
    # Randomly choose between zip code and email
    if random.choice([True, False]) and 'email' in user:
        contact_info = f"your email is {user['email']}"
    else:
        contact_info = f"your zip code is {user['address']['zip']}"
    
    personality = generate_personality()
    personality_str = ", ".join(personality)
    
    return f"Your name is {name} and {contact_info}. You are {personality_str}. "

# Generate return task
def generate_return_task(user_id: str, users: Dict, orders: Dict, products: Dict) -> Task:
    # Find delivered orders for this user
    user_orders = [oid for oid in users[user_id]['orders'] if oid in orders and orders[oid]['status'] == 'delivered']
    if not user_orders:
        return None
    
    order_id = random.choice(user_orders)
    order = orders[order_id]
    
    # Select items to return (1-3 items)
    items_to_return = random.sample(order['items'], min(len(order['items']), random.randint(1, 3)))
    item_ids = [item['item_id'] for item in items_to_return]
    item_names = [item['name'] for item in items_to_return]
    
    # Select payment method
    payment_methods = list(users[user_id]['payment_methods'].keys())
    # Prefer original payment method or gift card
    original_payment = order['payment_history'][0]['payment_method_id']
    if original_payment in payment_methods:
        payment_method_id = original_payment
    else:
        gift_cards = [pm for pm in payment_methods if 'gift_card' in pm]
        payment_method_id = random.choice(gift_cards) if gift_cards else random.choice(payment_methods)
    
    instruction = generate_user_prefix(user_id, users)
    instruction += f"Return {order_id} via {payment_method_id}: {'; '.join(item_names)}; "
    
    action = Action(
        name="return_delivered_order_items",
        kwargs={
            "order_id": order_id,
            "item_ids": item_ids,
            "payment_method_id": payment_method_id,
        }
    )
    
    return Task(
        annotator="synthetic",
        user_id=user_id,
        instruction=instruction,
        actions=[action],
        outputs=[]
    )

# Generate cancel task
def generate_cancel_task(user_id: str, users: Dict, orders: Dict) -> Task:
    # Find pending orders for this user
    user_orders = [oid for oid in users[user_id]['orders'] if oid in orders and orders[oid]['status'] == 'pending']
    if not user_orders:
        return None
    
    order_id = random.choice(user_orders)
    reason = random.choice(CANCEL_REASONS)
    
    instruction = generate_user_prefix(user_id, users)
    instruction += f"Cancel order {order_id} because {reason}. "
    
    action = Action(
        name="cancel_pending_order",
        kwargs={
            "order_id": order_id,
            "reason": reason
        }
    )
    
    return Task(
        annotator="synthetic",
        user_id=user_id,
        instruction=instruction,
        actions=[action],
        outputs=[]
    )

# Generate exchange task
def generate_exchange_task(user_id: str, users: Dict, orders: Dict, products: Dict) -> Task:
    # Find delivered orders for this user
    user_orders = [oid for oid in users[user_id]['orders'] if oid in orders and orders[oid]['status'] == 'delivered']
    if not user_orders:
        return None
    
    order_id = random.choice(user_orders)
    order = orders[order_id]
    
    # Select items to exchange (1-2 items)
    items_to_exchange = random.sample(order['items'], min(len(order['items']), random.randint(1, 2)))
    
    item_ids = []
    new_item_ids = []
    exchange_descriptions = []
    
    for item in items_to_exchange:
        product_id = item['product_id']
        product = products[product_id]
        
        # Find available variants of the same product
        available_variants = [v for v in product['variants'].values() if v['available'] and v['item_id'] != item['item_id']]
        if not available_variants:
            continue
            
        new_variant = random.choice(available_variants)
        
        item_ids.append(item['item_id'])
        new_item_ids.append(new_variant['item_id'])
        
        # Generate description of the change
        old_options = item['options']
        new_options = new_variant['options']
        
        changed_options = {}
        for key, new_value in new_options.items():
            if key in old_options and old_options[key] != new_value:
                changed_options[key] = new_value
        
        if changed_options:
            exchange_descriptions.append(f"{item['name']} {old_options} to {changed_options}")
        else:
            exchange_descriptions.append(f"{item['name']} {old_options} to {{}}")
    
    if not item_ids:
        return None
    
    # Select payment method
    payment_methods = list(users[user_id]['payment_methods'].keys())
    payment_method_id = random.choice(payment_methods)
    
    instruction = generate_user_prefix(user_id, users)
    instruction += f"For {order_id}, exchange {'; '.join(exchange_descriptions)}; via {payment_method_id}. "
    
    action = Action(
        name="exchange_delivered_order_items",
        kwargs={
            "order_id": order_id,
            "item_ids": item_ids,
            "new_item_ids": new_item_ids,
            "payment_method_id": payment_method_id,
        }
    )
    
    return Task(
        annotator="synthetic",
        user_id=user_id,
        instruction=instruction,
        actions=[action],
        outputs=[]
    )

# Generate modify pending order items task
def generate_modify_items_task(user_id: str, users: Dict, orders: Dict, products: Dict) -> Task:
    # Find pending orders for this user
    user_orders = [oid for oid in users[user_id]['orders'] if oid in orders and orders[oid]['status'] == 'pending']
    if not user_orders:
        return None

    order_id = random.choice(user_orders)
    order = orders[order_id]

    # Select items to modify (1-2 items)
    items_to_modify = random.sample(order['items'], min(len(order['items']), random.randint(1, 2)))

    item_ids = []
    new_item_ids = []
    modify_descriptions = []

    for item in items_to_modify:
        product_id = item['product_id']
        product = products[product_id]

        # Find available variants of the same product
        available_variants = [v for v in product['variants'].values() if v['available']]
        if not available_variants:
            continue

        # Sometimes keep the same item (no change)
        if random.random() < 0.3:
            new_variant = next(v for v in available_variants if v['item_id'] == item['item_id'])
        else:
            other_variants = [v for v in available_variants if v['item_id'] != item['item_id']]
            if not other_variants:
                new_variant = next(v for v in available_variants if v['item_id'] == item['item_id'])
            else:
                new_variant = random.choice(other_variants)

        item_ids.append(item['item_id'])
        new_item_ids.append(new_variant['item_id'])

        # Generate description of the change
        old_options = item['options']
        new_options = new_variant['options']

        changed_options = {}
        for key, new_value in new_options.items():
            if key in old_options and old_options[key] != new_value:
                changed_options[key] = new_value

        if changed_options:
            modify_descriptions.append(f"{item['name']} {old_options} to {changed_options}")
        else:
            modify_descriptions.append(f"{item['name']} {old_options} to {{}}")

    if not item_ids:
        return None

    # Select payment method
    payment_methods = list(users[user_id]['payment_methods'].keys())
    payment_method_id = random.choice(payment_methods)

    instruction = generate_user_prefix(user_id, users)
    instruction += f"For {order_id}, modify {'; '.join(modify_descriptions)}; via {payment_method_id}. "

    action = Action(
        name="modify_pending_order_items",
        kwargs={
            "order_id": order_id,
            "item_ids": item_ids,
            "new_item_ids": new_item_ids,
            "payment_method_id": payment_method_id,
        }
    )

    return Task(
        annotator="synthetic",
        user_id=user_id,
        instruction=instruction,
        actions=[action],
        outputs=[]
    )

# Generate modify payment task
def generate_modify_payment_task(user_id: str, users: Dict, orders: Dict) -> Task:
    # Find pending orders for this user
    user_orders = [oid for oid in users[user_id]['orders'] if oid in orders and orders[oid]['status'] == 'pending']
    if not user_orders:
        return None

    order_id = random.choice(user_orders)

    # Select different payment method
    payment_methods = list(users[user_id]['payment_methods'].keys())
    if len(payment_methods) < 2:
        return None

    current_payment = orders[order_id]['payment_history'][0]['payment_method_id']
    other_payments = [pm for pm in payment_methods if pm != current_payment]
    if not other_payments:
        return None

    new_payment_method_id = random.choice(other_payments)

    instruction = generate_user_prefix(user_id, users)
    instruction += f"For {order_id}, change payment to {new_payment_method_id}. "

    action = Action(
        name="modify_pending_order_payment",
        kwargs={
            "order_id": order_id,
            "payment_method_id": new_payment_method_id,
        }
    )

    return Task(
        annotator="synthetic",
        user_id=user_id,
        instruction=instruction,
        actions=[action],
        outputs=[]
    )

# Generate modify address task
def generate_modify_address_task(user_id: str, users: Dict, orders: Dict) -> Task:
    # Find pending orders for this user
    user_orders = [oid for oid in users[user_id]['orders'] if oid in orders and orders[oid]['status'] == 'pending']
    if not user_orders:
        return None

    order_id = random.choice(user_orders)

    # Generate new address (similar to existing but different)
    user = users[user_id]
    original_address = user['address']

    # Generate variations
    streets = ["Park Avenue", "Main Street", "Broadway", "Elm Avenue", "Oak Street", "Cedar Street", "Sunset Drive", "River Road"]
    cities = ["New York", "Los Angeles", "Chicago", "Houston", "Phoenix", "Philadelphia", "San Antonio", "Dallas"]
    states = ["NY", "CA", "IL", "TX", "AZ", "PA", "FL", "OH"]

    new_address = {
        "address1": f"{random.randint(100, 999)} {random.choice(streets)}",
        "address2": f"Suite {random.randint(100, 999)}",
        "city": random.choice(cities),
        "country": "USA",
        "state": random.choice(states),
        "zip": f"{random.randint(10000, 99999)}"
    }

    # Reference another order for the address
    other_orders = [oid for oid in users[user_id]['orders'] if oid != order_id and oid in orders]
    reference_order = random.choice(other_orders) if other_orders else f"#W{random.randint(1000000, 9999999)}"

    instruction = generate_user_prefix(user_id, users)
    instruction += f"For {order_id}, change address to {new_address} (same as {reference_order}). "

    action = Action(
        name="modify_pending_order_address",
        kwargs={
            "order_id": order_id,
            "address1": new_address["address1"],
            "address2": new_address["address2"],
            "city": new_address["city"],
            "country": new_address["country"],
            "state": new_address["state"],
            "zip": new_address["zip"],
        }
    )

    return Task(
        annotator="synthetic",
        user_id=user_id,
        instruction=instruction,
        actions=[action],
        outputs=[]
    )

# Generate complex multi-action task
def generate_complex_task(user_id: str, users: Dict, orders: Dict, products: Dict) -> Task:
    actions = []
    instruction_parts = []

    # Start with user prefix
    instruction = generate_user_prefix(user_id, users)

    # Randomly combine 2-4 different actions
    num_actions = random.randint(2, 4)
    possible_actions = []

    # Check what actions are possible for this user
    pending_orders = [oid for oid in users[user_id]['orders'] if oid in orders and orders[oid]['status'] == 'pending']
    delivered_orders = [oid for oid in users[user_id]['orders'] if oid in orders and orders[oid]['status'] == 'delivered']

    if pending_orders:
        possible_actions.extend(['cancel', 'modify_payment', 'modify_items', 'modify_address'])
    if delivered_orders:
        possible_actions.extend(['return', 'exchange'])

    if len(possible_actions) < 2:
        return None

    selected_actions = random.sample(possible_actions, min(num_actions, len(possible_actions)))

    for action_type in selected_actions:
        if action_type == 'cancel':
            if pending_orders:
                order_id = random.choice(pending_orders)
                reason = random.choice(CANCEL_REASONS)
                instruction_parts.append(f"Cancel order {order_id} because {reason}")
                actions.append(Action(
                    name="cancel_pending_order",
                    kwargs={"order_id": order_id, "reason": reason}
                ))
                # Remove from pending orders to avoid conflicts
                if order_id in pending_orders:
                    pending_orders.remove(order_id)

        elif action_type == 'modify_payment' and pending_orders:
            order_id = random.choice(pending_orders)
            payment_methods = list(users[user_id]['payment_methods'].keys())
            if len(payment_methods) > 1:
                current_payment = orders[order_id]['payment_history'][0]['payment_method_id']
                other_payments = [pm for pm in payment_methods if pm != current_payment]
                if other_payments:
                    new_payment = random.choice(other_payments)
                    instruction_parts.append(f"For {order_id}, change payment to {new_payment}")
                    actions.append(Action(
                        name="modify_pending_order_payment",
                        kwargs={"order_id": order_id, "payment_method_id": new_payment}
                    ))

        elif action_type == 'return' and delivered_orders:
            order_id = random.choice(delivered_orders)
            order = orders[order_id]
            items_to_return = random.sample(order['items'], min(len(order['items']), random.randint(1, 2)))
            item_ids = [item['item_id'] for item in items_to_return]
            item_names = [item['name'] for item in items_to_return]

            payment_methods = list(users[user_id]['payment_methods'].keys())
            payment_method_id = random.choice(payment_methods)

            instruction_parts.append(f"Return {order_id} via {payment_method_id}: {'; '.join(item_names)}")
            actions.append(Action(
                name="return_delivered_order_items",
                kwargs={
                    "order_id": order_id,
                    "item_ids": item_ids,
                    "payment_method_id": payment_method_id,
                }
            ))
            # Remove from delivered orders to avoid conflicts
            if order_id in delivered_orders:
                delivered_orders.remove(order_id)

    if not actions:
        return None

    instruction += ". ".join(instruction_parts) + ". "

    return Task(
        annotator="synthetic",
        user_id=user_id,
        instruction=instruction,
        actions=actions,
        outputs=[]
    )

def main():
    users, orders, products = load_data()

    # Get list of user IDs
    user_ids = list(users.keys())

    tasks = []
    # Weight the task types - complex tasks should be less common
    task_types = (
        ['return'] * 20 +
        ['cancel'] * 15 +
        ['exchange'] * 20 +
        ['modify_items'] * 15 +
        ['modify_payment'] * 10 +
        ['modify_address'] * 10 +
        ['complex'] * 10
    )

    # Generate 2000 tasks
    for i in range(2000):
        attempts = 0
        task = None

        while task is None and attempts < 20:
            user_id = random.choice(user_ids)
            task_type = random.choice(task_types)

            try:
                if task_type == 'return':
                    task = generate_return_task(user_id, users, orders, products)
                elif task_type == 'cancel':
                    task = generate_cancel_task(user_id, users, orders)
                elif task_type == 'exchange':
                    task = generate_exchange_task(user_id, users, orders, products)
                elif task_type == 'modify_items':
                    task = generate_modify_items_task(user_id, users, orders, products)
                elif task_type == 'modify_payment':
                    task = generate_modify_payment_task(user_id, users, orders)
                elif task_type == 'modify_address':
                    task = generate_modify_address_task(user_id, users, orders)
                elif task_type == 'complex':
                    task = generate_complex_task(user_id, users, orders, products)
            except Exception as e:
                print(f"Error generating {task_type} task for {user_id}: {e}")
                task = None

            attempts += 1

        if task:
            tasks.append(task)

        if (i + 1) % 100 == 0:
            print(f"Generated {i + 1} tasks...")

    print(f"Successfully generated {len(tasks)} tasks")
    
    # Write to file
    with open('tau_bench/envs/retail/tasks_train_new.py', 'w') as f:
        f.write("from tau_bench.types import Task, Action\n\n")
        f.write("TASKS_TRAIN_NEW = [\n")
        
        for task in tasks:
            f.write("    Task(\n")
            f.write(f'        annotator="synthetic",\n')
            f.write(f'        user_id="{task.user_id}",\n')
            f.write(f'        instruction="{task.instruction}",\n')
            f.write("        actions=[\n")
            
            for action in task.actions:
                f.write("            Action(\n")
                f.write(f'                name="{action.name}",\n')
                f.write("                kwargs={\n")
                for key, value in action.kwargs.items():
                    if isinstance(value, str):
                        f.write(f'                    "{key}": "{value}",\n')
                    elif isinstance(value, list):
                        f.write(f'                    "{key}": {value},\n')
                    else:
                        f.write(f'                    "{key}": {value},\n')
                f.write("                },\n")
                f.write("            "),
                if action != task.actions[-1]:
                    f.write(",")
                f.write("\n")
            
            f.write("        ],\n")
            f.write("        outputs=[],\n")
            f.write("    ),\n")
        
        f.write("]\n")

if __name__ == "__main__":
    main()
