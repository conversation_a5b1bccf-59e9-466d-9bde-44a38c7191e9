"""
Utility functions for handling LLM parameters across different providers.
"""

def build_completion_params(
    model: str,
    provider: str,
    messages,
    temperature: float,
    top_p: float | None = None,
    top_k: int | None = None,
    tools=None,
    client=None,
    **kwargs
) -> dict:
    """
    Build completion parameters for litellm, handling provider-specific parameter support.

    Args:
        model: Model name
        provider: Provider name (openai, anthropic, etc.)
        messages: Messages for the completion
        temperature: Temperature parameter
        top_p: Top-p parameter (nucleus sampling)
        top_k: Top-k parameter (not supported by all providers via litellm)
        tools: Tools for function calling
        client: Custom client
        **kwargs: Additional parameters

    Returns:
        Dictionary of parameters safe to pass to litellm completion()
    """
    params = {
        "model": model,
        "custom_llm_provider": provider,
        "messages": messages,
        "temperature": temperature,
        **kwargs
    }

    if tools is not None:
        params["tools"] = tools

    if client is not None:
        params["client"] = client

    # Add top_p if supported and provided
    if top_p is not None:
        # Most providers support top_p via litellm
        params["top_p"] = top_p

    # Note: top_k is not widely supported via litellm for most providers
    # We skip it for litellm calls to avoid errors
    # For direct API calls (deployed mode), we handle top_k separately

    return params


def clean_message_for_gemini(message):
    """
    Clean a message object to remove null values that might cause issues with Gemini API.

    Args:
        message: Message dictionary to clean

    Returns:
        Cleaned message dictionary
    """
    if not isinstance(message, dict):
        return message

    cleaned = {}
    for key, value in message.items():
        if value is None:
            continue  # Skip None values
        elif key == "tool_calls" and isinstance(value, list):
            # Clean tool_calls array
            cleaned_tool_calls = []
            for tool_call in value:
                if isinstance(tool_call, dict):
                    cleaned_tool_call = {}
                    for tc_key, tc_value in tool_call.items():
                        if tc_value is None:
                            continue  # Skip None values in tool_call
                        elif tc_key == "function" and isinstance(tc_value, dict):
                            # Clean function object
                            cleaned_function = {fk: fv for fk, fv in tc_value.items() if fv is not None}
                            if cleaned_function:  # Only add if not empty
                                cleaned_tool_call[tc_key] = cleaned_function
                        else:
                            cleaned_tool_call[tc_key] = tc_value
                    if cleaned_tool_call:  # Only add if not empty
                        cleaned_tool_calls.append(cleaned_tool_call)
            if cleaned_tool_calls:  # Only add if not empty
                cleaned[key] = cleaned_tool_calls
        elif isinstance(value, dict):
            # Recursively clean nested dictionaries
            cleaned_nested = clean_message_for_gemini(value)
            if cleaned_nested:  # Only add if not empty
                cleaned[key] = cleaned_nested
        elif isinstance(value, list):
            # Clean lists
            cleaned_list = [clean_message_for_gemini(item) for item in value if item is not None]
            if cleaned_list:  # Only add if not empty
                cleaned[key] = cleaned_list
        else:
            cleaned[key] = value

    return cleaned


def build_openai_api_params(
    model: str,
    messages,
    temperature: float,
    top_p: float | None = None,
    top_k: int | None = None,
    tools=None,
    type: str | None = None,
    budget_tokens: int | None = None,
    extra_body: dict | None = None,
    **kwargs
) -> dict:
    """
    Build parameters for direct OpenAI API calls.

    Args:
        model: Model name
        messages: Messages for the completion
        temperature: Temperature parameter
        top_p: Top-p parameter
        top_k: Top-k parameter (passed via extra_body)
        tools: Tools for function calling
        type: Claude thinking type parameter (passed via extra_body)
        budget_tokens: Claude thinking budget_tokens parameter (passed via extra_body)
        extra_body: Additional parameters to pass via extra_body
        **kwargs: Additional parameters

    Returns:
        Dictionary of parameters for OpenAI API
    """
    # Check if this is a Gemini model (based on model name)
    is_gemini_model = "gemini" in model.lower()

    # Clean messages for Gemini models to avoid null value issues
    if is_gemini_model:
        cleaned_messages = []
        for message in messages:
            cleaned_message = clean_message_for_gemini(message)
            if cleaned_message:  # Only add non-empty messages
                cleaned_messages.append(cleaned_message)
        messages = cleaned_messages

    params = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        **kwargs
    }

    if tools is not None:
        params["tools"] = tools

    # Add top_p if provided
    if top_p is not None:
        params["top_p"] = top_p

    # For OpenAI API, top_k and Claude thinking parameters need to be passed via extra_body
    # However, Gemini models through OpenAI-compatible endpoint may not support extra_body
    # Start with any extra_body parameters passed in
    merged_extra_body = extra_body.copy() if extra_body else {}

    if top_k is not None and not is_gemini_model:
        merged_extra_body["top_k"] = top_k

    # Add Claude thinking parameters to extra_body if both are provided
    if type is not None and budget_tokens is not None and not is_gemini_model:
        merged_extra_body["thinking"] = {
            "type": type,
            "budget_tokens": budget_tokens
        }

    # Only add extra_body if it has content
    if merged_extra_body:
        params["extra_body"] = merged_extra_body

    # Filter out any None values from kwargs to avoid potential issues with Gemini API
    if is_gemini_model:
        # Remove any None values that might cause issues
        params = {k: v for k, v in params.items() if v is not None}

    return params
