import json
import httpx
from typing import Any, Dict, List, Optional


class MinimaxMessage:
    def __init__(self, role: str, content: str, tool_calls: Optional[List[Dict[str, Any]]] = None):
        self.role = role
        self.content = content
        self.tool_calls = tool_calls

    def model_dump(self) -> Dict[str, Any]:
        """Return a dictionary representation compatible with OpenAI format."""
        result = {
            "role": self.role,
            "content": self.content
        }
        if self.tool_calls:
            result["tool_calls"] = self.tool_calls
        return result


class MinimaxChoice:
    def __init__(self, message: MinimaxMessage, finish_reason: str):
        self.message = message
        self.finish_reason = finish_reason


class MinimaxUsage:
    def __init__(self, prompt_tokens: int, completion_tokens: int, total_tokens: int):
        self.prompt_tokens = prompt_tokens
        self.completion_tokens = completion_tokens
        self.total_tokens = total_tokens


class MinimaxResponse:
    def __init__(self, choices: List[MinimaxChoice], usage: MinimaxUsage, _hidden_params: Dict[str, Any]):
        self.choices = choices
        self.usage = usage
        self._hidden_params = _hidden_params


class MinimaxClient:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.api_key = api_key

    def _convert_tools_to_minimax_format(self, tools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Convert OpenAI-style tools to MiniMax format."""
        minimax_tools = []
        for tool in tools:
            if tool.get("type") == "function":
                function_info = tool.get("function", {})
                minimax_tool = {
                    "type": "function",
                    "function": {
                        "name": function_info.get("name", ""),
                        "description": function_info.get("description", ""),
                        "parameters": function_info.get("parameters", {})
                    }
                }
                minimax_tools.append(minimax_tool)
        return minimax_tools

    def _simulate_tool_calling_response(self, messages: List[Dict[str, Any]], tools: List[Dict[str, Any]], **kwargs) -> MinimaxResponse:
        """
        Simulate tool calling for MiniMax by using text-based tool selection.
        Since MiniMax doesn't have native tool calling, we'll use a text-based approach.
        """
        # Build a prompt that includes tool information
        tool_descriptions = []
        for tool in tools:
            if tool.get("type") == "function":
                func = tool.get("function", {})
                tool_descriptions.append(f"- {func.get('name', '')}: {func.get('description', '')}")

        tools_text = "\n".join(tool_descriptions)

        # Add tool selection instruction to the last user message
        if messages and messages[-1].get("role") == "user":
            original_content = messages[-1]["content"]
            enhanced_content = f"""{original_content}

Available tools:
{tools_text}

If you need to use a tool, respond in this exact JSON format:
{{"tool_call": {{"name": "tool_name", "arguments": {{"param1": "value1"}}}}}}

If you don't need to use a tool, respond normally."""

            messages[-1]["content"] = enhanced_content

        # Make the API call
        api_params = {
            "model": kwargs.get("model", "MiniMax-Text-01"),
            "messages": messages,
            "temperature": kwargs.get("temperature", 0.0),
        }

        if "top_p" in kwargs and kwargs["top_p"] is not None:
            api_params["top_p"] = kwargs["top_p"]
        if "top_k" in kwargs and kwargs["top_k"] is not None:
            api_params["top_k"] = kwargs["top_k"]

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        with httpx.Client() as client:
            response = client.post(
                self.base_url,
                headers=headers,
                json=api_params,
                timeout=60.0
            )
            response.raise_for_status()
            res_data = response.json()

        # Parse the response
        if "choices" in res_data and len(res_data["choices"]) > 0:
            content = res_data["choices"][0].get("message", {}).get("content", "")

            # Try to parse tool call from response
            tool_calls = None
            try:
                # Check if response contains a tool call
                if "tool_call" in content:
                    # Try to extract JSON from the response
                    import re
                    json_match = re.search(r'\{.*"tool_call".*\}', content, re.DOTALL)
                    if json_match:
                        tool_data = json.loads(json_match.group())
                        tool_call_info = tool_data.get("tool_call", {})

                        # Create OpenAI-compatible tool call format
                        tool_calls = [{
                            "id": f"call_{hash(content) % 1000000}",
                            "type": "function",
                            "function": {
                                "name": tool_call_info.get("name", ""),
                                "arguments": json.dumps(tool_call_info.get("arguments", {}))
                            }
                        }]

                        # Clean the content to remove the tool call JSON
                        content = re.sub(r'\{.*"tool_call".*\}', '', content, flags=re.DOTALL).strip()
            except (json.JSONDecodeError, KeyError):
                # If parsing fails, treat as regular response
                pass

            # Create message object
            message = MinimaxMessage(role="assistant", content=content, tool_calls=tool_calls)

            choice = MinimaxChoice(
                message=message,
                finish_reason=res_data["choices"][0].get("finish_reason", "stop")
            )

            # Extract usage information
            usage_data = res_data.get("usage", {})
            usage = MinimaxUsage(
                prompt_tokens=usage_data.get("prompt_tokens", 0),
                completion_tokens=usage_data.get("completion_tokens", 0),
                total_tokens=usage_data.get("total_tokens", 0)
            )

            response_obj = MinimaxResponse(
                choices=[choice],
                usage=usage,
                _hidden_params={"response_cost": 0.0}
            )

            return response_obj
        else:
            # Empty response
            message = MinimaxMessage(role="assistant", content="", tool_calls=None)
            choice = MinimaxChoice(message=message, finish_reason="stop")
            usage = MinimaxUsage(prompt_tokens=0, completion_tokens=0, total_tokens=0)

            return MinimaxResponse(
                choices=[choice],
                usage=usage,
                _hidden_params={"response_cost": 0.0}
            )

    def chat_completion(self, **kwargs) -> MinimaxResponse:
        """Main chat completion method."""
        tools = kwargs.get("tools", [])

        if tools:
            # Use tool calling simulation
            return self._simulate_tool_calling_response(**kwargs)
        else:
            # Regular chat completion without tools
            messages = kwargs.get("messages", [])

            api_params = {
                "model": kwargs.get("model", "MiniMax-Text-01"),
                "messages": messages,
                "temperature": kwargs.get("temperature", 0.0),
            }

            if "top_p" in kwargs and kwargs["top_p"] is not None:
                api_params["top_p"] = kwargs["top_p"]
            if "top_k" in kwargs and kwargs["top_k"] is not None:
                api_params["top_k"] = kwargs["top_k"]

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
            }

            with httpx.Client() as client:
                response = client.post(
                    self.base_url,
                    headers=headers,
                    json=api_params,
                    timeout=60.0
                )
                response.raise_for_status()
                res_data = response.json()

            # Parse the response
            if "choices" in res_data and len(res_data["choices"]) > 0:
                content = res_data["choices"][0].get("message", {}).get("content", "")
                message = MinimaxMessage(role="assistant", content=content, tool_calls=None)
                choice = MinimaxChoice(
                    message=message,
                    finish_reason=res_data["choices"][0].get("finish_reason", "stop")
                )

                # Extract usage information
                usage_data = res_data.get("usage", {})
                usage = MinimaxUsage(
                    prompt_tokens=usage_data.get("prompt_tokens", 0),
                    completion_tokens=usage_data.get("completion_tokens", 0),
                    total_tokens=usage_data.get("total_tokens", 0)
                )

                return MinimaxResponse(
                    choices=[choice],
                    usage=usage,
                    _hidden_params={"response_cost": 0.0}
                )
            else:
                # Empty response
                message = MinimaxMessage(role="assistant", content="", tool_calls=None)
                choice = MinimaxChoice(message=message, finish_reason="stop")
                usage = MinimaxUsage(prompt_tokens=0, completion_tokens=0, total_tokens=0)

                return MinimaxResponse(
                    choices=[choice],
                    usage=usage,
                    _hidden_params={"response_cost": 0.0}
                )
