#!/bin/bash
# user_model 使用固定的 gpt-4o，方便各个模型之间 user 能够对齐
# 按顺序运行4种配置组合：env参数的retail、airline，agent-strategy的react、tool-calling

export DEPLOY_API_KEY=1234
export DEPLOY_BASE_URL=http://172.30.44.37:29998/v1

export OPENAI_API_KEY=None
# export OPENAI_API_KEY=********************************************************************************************************************************************************************
# export OPENAI_PROXY_URL=http://closeai-proxy.pjlab.org.cn:23128

# 定义配置组合
ENV_LIST="retail airline"
STRATEGY_LIST="tool-calling react"

echo "=========================================="
echo "开始按顺序执行4种配置组合："
echo "ENV: retail, airline"
echo "AGENT_STRATEGY: react, tool-calling"
echo "=========================================="

# 循环执行所有配置组合
for ENV in $ENV_LIST; do
    for STRATEGY in $STRATEGY_LIST; do
        echo ""
        echo "=========================================="
        echo "正在执行配置: ENV=$ENV, AGENT_STRATEGY=$STRATEGY"
        echo "=========================================="

        python /fs-computility/llmeval/zhudongsheng/program/tau-bench/run.py \
            --agent-strategy $STRATEGY \
            --env $ENV \
            --model Qwen/Qwen3-14B \
            --model-provider deployed \
            --base-url https://api.anthropic.com/v1/ \
            --temperature 0.6 \
            --top-k 20 \
            --top-p 0.95 \
            --user-model gpt-4o \
            --user-model-provider openai \
            --user-strategy llm \
            --max-concurrency 2 \

        echo "配置 ENV=$ENV, AGENT_STRATEGY=$STRATEGY 执行完毕"
        echo "=========================================="
    done
done

echo ""
echo "所有4种配置组合执行完毕！"
echo "执行顺序："
echo "1. ENV=retail, AGENT_STRATEGY=react"
echo "2. ENV=retail, AGENT_STRATEGY=tool-calling"
echo "3. ENV=airline, AGENT_STRATEGY=react"
echo "4. ENV=airline, AGENT_STRATEGY=tool-calling"
