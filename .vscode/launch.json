{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Run",
            "type": "debugpy",
            "request": "launch",
            "program": "/fs-computility/llmeval/zhudongsheng/program/tau-bench/run.py",
            "python": "/fs-computility/llmeval/zhudongsheng/miniconda3/envs/tau_bench/bin/python",
            "args": [
                "--agent-strategy", "tool-calling",
                "--env", "retail",
                "--model", "tooleval-InternVL3-235B-Qwen3MoE-20250702d-7500-cpt-data-0628-sft-science-data-0703-slow-tokenize-lr-8e5-rjob-h200_18",
                "--model-provider", "deployed",
                "--base-url", "http://172.30.17.239:29998/v1",
                "--temperature", "0.6", 
                "--top-p", "0.95", 
                "--top-k", "20",
                "--user-model", "gpt-4o",
                "--user-model-provider","openai",
                "--user-strategy", "llm",
                "--max-concurrency", "1",
            ],
            "console": "integratedTerminal",
            "env": {
                // // "CUDA_VISIBLE_DEVICES": "0,1",
                "OPENAI_API_KEY": "************************************************************************************************************", 
                "DEPLOY_BASE_URL": "http://106.15.231.215:40007/v1",
                "DEPLOY_API_KEY": "sk-admin",
                // "OPENAI_PROXY_URL": "http://closeai-proxy.pjlab.org.cn:23128", 
                // "HTTP_PROXY": "http://closeai-proxy.pjlab.org.cn:23128", 
                // "https_proxy": "http://closeai-proxy.pjlab.org.cn:23128",
                // "http_proxy": "http://closeai-proxy.pjlab.org.cn:23128",
            }
        }
    ]
}