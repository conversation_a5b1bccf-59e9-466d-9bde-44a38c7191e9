import openai
import httpx

proxy = "http://closeai-proxy.pjlab.org.cn:23128"
# client = openai.Client(
#     base_url = "https://generativelanguage.googleapis.com/v1beta/openai/", api_key="AIzaSyDMti4Dz9BhC2rBbS0BAWpSNyu0bR-Yxeo",
#     http_client=httpx.Client(proxy=proxy))

# completion = client.chat.completions.create(
#   model="gemini-2.5-pro-preview-05-06",
#   messages=[
#     {"role": "user", "content": "Hello!"}
#   ]
# )

client = openai.Client(
    base_url = "https://generativelanguage.googleapis.com/v1beta/openai/", api_key="AIzaSyDMti4Dz9BhC2rBbS0BAWpSNyu0bR-Yxeo",
    http_client=httpx.Client(proxy=proxy))

completion = client.chat.completions.create(
  model="gemini-2.5-pro-preview-05-06",
  messages=[
    {"role": "system", "content": "You are a user interacting with an agent. The agent is a customer service representative for an online store. The user is trying to get help with an order. The user is friendly and polite, while the agent is professional and helpful. The user has a specific order they need assistance with, but they don't remember the order ID. The agent should ask for the order ID to assist the user further. The conversation should be natural and flow smoothly."},
    {"role": "user", "content": "Hello!"}
  ],
  # tool_choice='none'
)

print(completion.choices[0].message)
